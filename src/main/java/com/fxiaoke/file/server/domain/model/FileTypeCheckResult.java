package com.fxiaoke.file.server.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 文件类型检查结果
 *
 * <AUTHOR> Agent
 * @since 2025-08-18
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FileTypeCheckResult {

  /**
   * 文件的真实类型（通过文件头魔数检测得出） 可能包含多个类型，用逗号分隔，如："jpg,jpeg,jpe,jfif"
   */
  private String actualType;

  /**
   * 文件的声明类型（用户上传时声明的扩展名）
   */
  private String declaredType;

  /**
   * 真实类型是否在允许上传的白名单中
   */
  private boolean actualTypeInWhitelist;

  /**
   * 声明类型是否在允许上传的白名单中
   */
  private boolean declaredTypeInWhitelist;

  /**
   * 声明类型与真实类型是否匹配
   */
  private boolean typeMatching;

  /**
   * 检查是否通过（综合判断结果）
   */
  private boolean checkPassed;

  /**
   * 检查失败的原因（当 checkPassed 为 false 时）
   */
  private String failureReason;

  /**
   * 是否跳过了精确检查（由于配置或文件大小等原因）
   */
  private boolean exactCheckSkipped;

  /**
   * 跳过精确检查的原因
   */
  private String skipReason;

  /**
   * 创建一个检查通过的结果
   */
  public static FileTypeCheckResult passed(String actualType, String declaredType,
      boolean actualInWhitelist, boolean declaredInWhitelist,
      boolean typeMatching) {
    return FileTypeCheckResult.builder()
        .actualType(actualType)
        .declaredType(declaredType)
        .actualTypeInWhitelist(actualInWhitelist)
        .declaredTypeInWhitelist(declaredInWhitelist)
        .typeMatching(typeMatching)
        .checkPassed(true)
        .exactCheckSkipped(false)
        .build();
  }

  /**
   * 创建一个检查失败的结果
   */
  public static FileTypeCheckResult failed(String actualType, String declaredType,
      boolean actualInWhitelist, boolean declaredInWhitelist,
      boolean typeMatching, String failureReason) {
    return FileTypeCheckResult.builder()
        .actualType(actualType)
        .declaredType(declaredType)
        .actualTypeInWhitelist(actualInWhitelist)
        .declaredTypeInWhitelist(declaredInWhitelist)
        .typeMatching(typeMatching)
        .checkPassed(false)
        .failureReason(failureReason)
        .exactCheckSkipped(false)
        .build();
  }

  /**
   * 创建一个跳过精确检查的结果
   */
  public static FileTypeCheckResult skipped(String declaredType, String skipReason) {
    return FileTypeCheckResult.builder()
        .actualType(declaredType)
        .declaredType(declaredType)
        .actualTypeInWhitelist(true) // 跳过时实际类型不检查
        .declaredTypeInWhitelist(true) // 跳过时声明类型不检查
        .typeMatching(true) // 跳过时不进行类型匹配检查
        .checkPassed(true)
        .exactCheckSkipped(true)
        .skipReason(skipReason)
        .build();
  }
}
