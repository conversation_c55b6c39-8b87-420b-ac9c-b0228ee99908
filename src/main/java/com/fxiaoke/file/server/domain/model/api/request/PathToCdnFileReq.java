package com.fxiaoke.file.server.domain.model.api.request;

import com.fxiaoke.file.server.domain.constants.PathType;
import com.fxiaoke.file.server.utils.PathStrUtil;
import com.fxiaoke.file.server.validation.ValidPathToCdnFileReq;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@ValidPathToCdnFileReq
public class PathToCdnFileReq {
  /**
   * 文件所属企业
   */

  @NotNull
  @Min(value = 1, message = "tenantId must be greater than or equal to 1")
  private Long tenantId;

  /**
   * 用户ID
   * 注：如果是系统调用则传 -10000
   */
  @NotNull
  private Long employeeId;

  /**
   * 业务线标识（必须申请注册,存在校验）
   */
  @NotBlank(message = "business not blank")
  private String business;

  /**
   * 业务线子标识 (允许业务线自行分组)
   * 必须由小写字母、数字、下划线、连字符组成,长度不超过32个字符
   */
  @NotBlank(message = "businessUnit not blank")
  @Size(min = 1, max = 32, message = "businessUnit length must be between 1 and 32 characters")
  @Pattern(
      regexp = "^[a-z0-9_-]+$",
      message = "businessUnit must consist of lowercase letters, numbers, underscores, or hyphens"
  )
  private String businessUnit;

  /**
   * 支持N|TN|C|TC
   */
  @NotBlank(message = "path not blank")
  @Pattern(
      regexp = "^(N_|TN_|C_|TC_)$",
      message = "path must be one of N_, TN_, C_, TC_"
  )
  private String path;

  /**
   * 原始文件名
   * 注：可能没有
   */
  private String name;

  /**
   * 当前仅支持 图片（jpg、webp、png、jpeg、bmp)
   */
  @NotBlank(message = "extension not blank")
  @Size(min = 1, max = 10, message = "extension length must be between 1 and 10 characters")
  private String extension;

  /**
   * 文件Hash（用于幂等性校验）
   * 注：可能没有
   */
  private String hashCode;

  /**
   * 打标记（标识业务、标识一类数据、最多10个）
   * 每个标签必须由小写字母、数字、下划线、连字符组成，长度不超过20个字符
   * 最多10个标签
   * 注：可能没有
   */
  @Size(max = 10, message = "tags size must be less than or equal to 10")
  private List<String> tags;

  /**
   * 描述（描述图片的作用及内容，也可用于备注）
   * 长度不超过200个字符
   * 注：可能没有
   */
  @Size(max = 200, message = "description length must be less than or equal to 200 characters")
  private String description;

  public String getPath(){
    PathType pathType = PathStrUtil.getPathType(path);
    return PathStrUtil.getBasePath(pathType, path);
  }
}
