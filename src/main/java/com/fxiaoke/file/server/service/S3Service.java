package com.fxiaoke.file.server.service;

import com.fxiaoke.file.server.domain.model.S3ObjectMetadata;
import com.fxiaoke.file.server.domain.model.S3ObjectPutReq;
import com.fxiaoke.file.server.domain.model.S3ObjectPutRes;
import java.io.InputStream;

public interface S3Service {

  S3ObjectMetadata getS3ObjectMetaInfo(String ea, String bucketName, String objectKey);

  S3ObjectPutRes putObject(S3ObjectPutReq req, InputStream stream);
}
