package com.fxiaoke.file.server.service.impl;

import com.facishare.restful.client.exception.FRestClientException;
import com.facishare.stone.sdk.StoneProxyApi;
import com.facishare.stone.sdk.request.StoneFileDownloadRequest;
import com.fxiaoke.file.server.config.CmsPropertiesConfig;
import com.fxiaoke.file.server.dao.mongo.CdnFileMetaDao;
import com.fxiaoke.file.server.domain.constants.Constant;
import com.fxiaoke.file.server.domain.entity.CdnFileMeta;
import com.fxiaoke.file.server.domain.entity.NFileMeta;
import com.fxiaoke.file.server.domain.exception.FileServerException;
import com.fxiaoke.file.server.domain.model.FileTypeCheckResult;
import com.fxiaoke.file.server.domain.model.S3ObjectPutReq;
import com.fxiaoke.file.server.domain.model.S3ObjectPutRes;
import com.fxiaoke.file.server.domain.model.api.request.PathToCdnFileReq;
import com.fxiaoke.file.server.help.FileTypeCheckHelp;
import com.fxiaoke.file.server.service.AsmService;
import com.fxiaoke.file.server.service.CdnFileService;
import com.fxiaoke.file.server.service.FileMetaService;
import com.fxiaoke.file.server.service.S3Service;
import com.fxiaoke.file.server.utils.CodingUtil;
import com.fxiaoke.file.server.utils.DataTimeFormatUtil;
import com.fxiaoke.file.server.utils.StrUtils;
import java.io.BufferedInputStream;
import java.io.InputStream;
import java.util.Date;
import org.bson.types.ObjectId;
import org.springframework.stereotype.Service;

@Service
public class CdnFileServiceImpl implements CdnFileService {

  private final static String MODULE = "CdnFileService";

  private final S3Service s3Service;

  private final StoneProxyApi nFile;

  private final AsmService asmService;

  private final CmsPropertiesConfig config;

  private final CdnFileMetaDao cdnFileMetaDao;

  private final FileMetaService fileMetaService;

  private final FileTypeCheckHelp fileTypeCheckHelp;

  public CdnFileServiceImpl(S3Service s3Service, StoneProxyApi nFile, AsmService asmService,
      CmsPropertiesConfig config, CdnFileMetaDao cdnFileMetaDao, FileMetaService fileMetaService,
      FileTypeCheckHelp fileTypeCheckHelp) {
    this.s3Service = s3Service;
    this.nFile = nFile;
    this.asmService = asmService;
    this.config = config;
    this.cdnFileMetaDao = cdnFileMetaDao;
    this.fileMetaService = fileMetaService;
    this.fileTypeCheckHelp = fileTypeCheckHelp;
  }

  @Override
  public String pathToCdnFile(PathToCdnFileReq req) {

    try {
      // 获取文件元数据
      int tenantId = Math.toIntExact(req.getTenantId());
      int employeeId = Math.toIntExact(req.getEmployeeId());
      String ea = asmService.getEa(tenantId);
      NFileMeta nFileMeta = fileMetaService.find(ea, req.getPath());
      long size = nFileMeta.getSize();

      // 校验文件大小
      CodingUtil.throwIfFalse(size > config.getCdnMaxFileSize(),
          "PathToCdnFileReq.size exceeds maximum allowed size: " + size, req);

      // 获取文件流
      try (BufferedInputStream stream = new BufferedInputStream(
          getNCFileByStone(ea, employeeId, req.getPath(), nFileMeta.getExtension()))) {

        // 从流中读取文件魔数并获取文件真实类型，仅支持配置的类型转换为 CDN 文件
        FileTypeCheckResult fileTypeCheckResult = fileTypeCheckHelp.checkWithResult(stream, ea,
            size, nFileMeta.getExtension());
        CodingUtil.throwIfFalse(!fileTypeCheckResult.isCheckPassed(),
            fileTypeCheckResult.getFailureReason(), req);
        // 文件的真实类型
        String fileActualType = fileTypeCheckResult.getActualType();

        // 使用 ObjectId 生成唯一标识符作为对象存储文件名与 MongoDB 文件元数据的 ID
        ObjectId objectId = new ObjectId();
        // s3Service 上传到 S3
        S3ObjectPutRes s3ObjectPutRes = putObjectToS3(req, objectId, fileActualType, stream);
        // cdnFileMetaDao 保存文件元数据到 MongoDB
        saveCdnFileMeta(req, nFileMeta, objectId, fileActualType, s3ObjectPutRes);

        // 返回 CDN 文件的 请求地址（无域名） 即 cdnReqPath
        return s3ObjectPutRes.getObjectKey();
      }
    } catch (FileServerException e) {
      throw e;
    } catch (Exception e) {
      // 处理异常情况
      throw new FileServerException(e, MODULE, 500, "Upload file to cdn fail", req);
    }
  }

  public String getName(PathToCdnFileReq req, NFileMeta nFileMeta) {
    if (StrUtils.notBlank(req.getName())) {
      return req.getName();
    }
    return nFileMeta.getName();
  }

  public InputStream getNCFileByStone(String ea, Integer employeeId, String path, String extension)
      throws FRestClientException {
    StoneFileDownloadRequest request = new StoneFileDownloadRequest();
    request.setEa(ea);
    request.setEmployeeId(employeeId);
    request.setPath(path);
    request.setBusiness(Constant.BUSINESS);
    request.setSecurityGroup(Constant.DEFAULT_SECURITY_GROUP);
    request.setFileType(extension);
    request.setCancelRemoteThumb(true);
    return nFile.downloadStream(request);
  }

  // srs/{yyyyMM}/{business}/{businessUnit}/{dd}/{fileName}.{extension}
  public String generateObjectKey(String business, String businessUnit, String fileName,
      String extension) {
    String cdnFileS3BucketPrefix = config.getCdnFileS3BucketPrefix();
    StringBuilder objectKey = new StringBuilder(cdnFileS3BucketPrefix);
    objectKey.append(DataTimeFormatUtil.getCurrentYear()).append("/").append(business).append("/");
    if (StrUtils.notBlank(businessUnit)) {
      objectKey.append(businessUnit).append("/");
    }
    objectKey.append(DataTimeFormatUtil.getCurrentDay()).append("/");
    objectKey.append(fileName).append(".").append(extension);

    return objectKey.toString();
  }

  public S3ObjectPutRes putObjectToS3(PathToCdnFileReq req, ObjectId objectId,
      String fileActualType, InputStream stream) {
    S3ObjectPutReq s3ObjectPutReq = new S3ObjectPutReq();
    s3ObjectPutReq.setEa(config.getCdnFileS3Ea());
    s3ObjectPutReq.setBucket(config.getCdnFileS3Bucket());
    String objectKey = generateObjectKey(req.getBusiness(), req.getBusinessUnit(),
        objectId.toString(), fileActualType);
    s3ObjectPutReq.setObjectKey(objectKey);
    return s3Service.putObject(s3ObjectPutReq, stream);
  }

  public void saveCdnFileMeta(PathToCdnFileReq req, NFileMeta nFileMeta, ObjectId objectId,
      String fileActualType, S3ObjectPutRes s3ObjectPutRes) {
    CdnFileMeta cdnFileMeta = new CdnFileMeta();
    cdnFileMeta.set_id(objectId);
    cdnFileMeta.setEa(nFileMeta.getEa());
    cdnFileMeta.setEmployeeId(req.getEmployeeId());
    cdnFileMeta.setOriginalPath(req.getPath());

    cdnFileMeta.setBusiness(req.getBusiness());
    cdnFileMeta.setBusinessUnit(req.getBusinessUnit());
    cdnFileMeta.setTags(req.getTags());
    cdnFileMeta.setDescription(req.getDescription());

    cdnFileMeta.setSize(nFileMeta.getSize());
    // 如果请求中有 name 则使用请求中的 name，否则使用 nFileMeta 中
    String name = getName(req, nFileMeta);
    cdnFileMeta.setName(name);
    cdnFileMeta.setHashCode(s3ObjectPutRes.getETag());
    cdnFileMeta.setExtension(fileActualType);

    cdnFileMeta.setCdnReqPath(s3ObjectPutRes.getObjectKey());
    cdnFileMeta.setStatus(true);
    cdnFileMeta.setCreateDate(new Date());

    cdnFileMetaDao.create(cdnFileMeta);
  }

}
