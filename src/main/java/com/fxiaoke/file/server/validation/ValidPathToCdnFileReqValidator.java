package com.fxiaoke.file.server.validation;

import com.fxiaoke.file.server.domain.model.api.request.PathToCdnFileReq;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

public class ValidPathToCdnFileReqValidator implements
    ConstraintValidator<ValidPathToCdnFileReq, PathToCdnFileReq> {

  @Override
  public boolean isValid(PathToCdnFileReq value, ConstraintValidatorContext context) {
    return false;
  }
}
