package com.fxiaoke.file.server.validation;

import com.fxiaoke.file.server.domain.model.api.request.PathToCdnFileReq;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Nested;

import java.util.Arrays;
import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ValidPathToCdnFileReqValidator 测试类
 */
@DisplayName("PathToCdnFileReq 自定义校验器测试")
class ValidPathToCdnFileReqValidatorTest {

    private Validator validator;

    @BeforeEach
    void setUp() {
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        validator = factory.getValidator();
    }

    @Nested
    @DisplayName("有效请求测试")
    class ValidRequestTests {

        @Test
        @DisplayName("完整有效请求应该通过校验")
        void shouldPassValidationForCompleteValidRequest() {
            PathToCdnFileReq request = createValidRequest();
            
            Set<ConstraintViolation<PathToCdnFileReq>> violations = validator.validate(request);
            
            assertTrue(violations.isEmpty(), "完整有效请求应该通过校验");
        }

        @Test
        @DisplayName("系统调用请求应该通过校验")
        void shouldPassValidationForSystemCall() {
            PathToCdnFileReq request = createValidRequest();
            request.setEmployeeId(-10000L); // 系统调用
            
            Set<ConstraintViolation<PathToCdnFileReq>> violations = validator.validate(request);
            
            assertTrue(violations.isEmpty(), "系统调用请求应该通过校验");
        }

        @Test
        @DisplayName("不同路径类型应该通过校验")
        void shouldPassValidationForDifferentPathTypes() {
            String[] validPaths = {"N_", "TN_", "C_", "TC_"};
            
            for (String path : validPaths) {
                PathToCdnFileReq request = createValidRequest();
                request.setPath(path);
                
                Set<ConstraintViolation<PathToCdnFileReq>> violations = validator.validate(request);
                
                assertTrue(violations.isEmpty(), 
                    String.format("路径类型 %s 应该通过校验", path));
            }
        }

        @Test
        @DisplayName("支持的图片扩展名应该通过校验")
        void shouldPassValidationForSupportedImageExtensions() {
            String[] validExtensions = {"jpg", "jpeg", "png", "webp", "bmp"};
            
            for (String extension : validExtensions) {
                PathToCdnFileReq request = createValidRequest();
                request.setExtension(extension);
                
                Set<ConstraintViolation<PathToCdnFileReq>> violations = validator.validate(request);
                
                assertTrue(violations.isEmpty(), 
                    String.format("图片扩展名 %s 应该通过校验", extension));
            }
        }
    }

    @Nested
    @DisplayName("无效请求测试")
    class InvalidRequestTests {

        @Test
        @DisplayName("无效的employeeId应该校验失败")
        void shouldFailValidationForInvalidEmployeeId() {
            Long[] invalidEmployeeIds = {null, 0L, -1L, -999L};
            
            for (Long employeeId : invalidEmployeeIds) {
                PathToCdnFileReq request = createValidRequest();
                request.setEmployeeId(employeeId);
                
                Set<ConstraintViolation<PathToCdnFileReq>> violations = validator.validate(request);
                
                assertFalse(violations.isEmpty(), 
                    String.format("employeeId %s 应该校验失败", employeeId));
            }
        }

        @Test
        @DisplayName("无效的路径格式应该校验失败")
        void shouldFailValidationForInvalidPathFormat() {
            String[] invalidPaths = {"", "A_", "TA_", "G_", "invalid", "N", "TN", "C", "TC"};
            
            for (String path : invalidPaths) {
                PathToCdnFileReq request = createValidRequest();
                request.setPath(path);
                
                Set<ConstraintViolation<PathToCdnFileReq>> violations = validator.validate(request);
                
                assertFalse(violations.isEmpty(), 
                    String.format("路径 %s 应该校验失败", path));
            }
        }

        @Test
        @DisplayName("不支持的文件扩展名应该校验失败")
        void shouldFailValidationForUnsupportedExtensions() {
            String[] invalidExtensions = {"", "txt", "pdf", "doc", "gif", "svg", "tiff"};
            
            for (String extension : invalidExtensions) {
                PathToCdnFileReq request = createValidRequest();
                request.setExtension(extension);
                
                Set<ConstraintViolation<PathToCdnFileReq>> violations = validator.validate(request);
                
                assertFalse(violations.isEmpty(), 
                    String.format("扩展名 %s 应该校验失败", extension));
            }
        }

        @Test
        @DisplayName("无效的标签格式应该校验失败")
        void shouldFailValidationForInvalidTags() {
            PathToCdnFileReq request = createValidRequest();
            
            // 测试包含大写字母的标签
            request.setTags(Arrays.asList("Valid-tag", "Invalid-Tag"));
            Set<ConstraintViolation<PathToCdnFileReq>> violations = validator.validate(request);
            assertFalse(violations.isEmpty(), "包含大写字母的标签应该校验失败");
            
            // 测试超长标签
            request.setTags(Arrays.asList("this-is-a-very-long-tag-name-that-exceeds-twenty-characters"));
            violations = validator.validate(request);
            assertFalse(violations.isEmpty(), "超长标签应该校验失败");
            
            // 测试包含特殊字符的标签
            request.setTags(Arrays.asList("invalid@tag", "invalid#tag"));
            violations = validator.validate(request);
            assertFalse(violations.isEmpty(), "包含特殊字符的标签应该校验失败");
        }

        @Test
        @DisplayName("无效的tenantId应该校验失败")
        void shouldFailValidationForInvalidTenantId() {
            Long[] invalidTenantIds = {null, 0L, -1L};
            
            for (Long tenantId : invalidTenantIds) {
                PathToCdnFileReq request = createValidRequest();
                request.setTenantId(tenantId);
                
                Set<ConstraintViolation<PathToCdnFileReq>> violations = validator.validate(request);
                
                assertFalse(violations.isEmpty(), 
                    String.format("tenantId %s 应该校验失败", tenantId));
            }
        }
    }

    @Nested
    @DisplayName("边界条件测试")
    class BoundaryTests {

        @Test
        @DisplayName("空标签列表应该通过校验")
        void shouldPassValidationForEmptyTagsList() {
            PathToCdnFileReq request = createValidRequest();
            request.setTags(Arrays.asList());
            
            Set<ConstraintViolation<PathToCdnFileReq>> violations = validator.validate(request);
            
            assertTrue(violations.isEmpty(), "空标签列表应该通过校验");
        }

        @Test
        @DisplayName("null标签列表应该通过校验")
        void shouldPassValidationForNullTagsList() {
            PathToCdnFileReq request = createValidRequest();
            request.setTags(null);
            
            Set<ConstraintViolation<PathToCdnFileReq>> violations = validator.validate(request);
            
            assertTrue(violations.isEmpty(), "null标签列表应该通过校验");
        }

        @Test
        @DisplayName("最大长度的有效标签应该通过校验")
        void shouldPassValidationForMaxLengthValidTag() {
            PathToCdnFileReq request = createValidRequest();
            request.setTags(Arrays.asList("12345678901234567890")); // 20个字符
            
            Set<ConstraintViolation<PathToCdnFileReq>> violations = validator.validate(request);
            
            assertTrue(violations.isEmpty(), "最大长度的有效标签应该通过校验");
        }
    }

    /**
     * 创建一个有效的请求对象
     */
    private PathToCdnFileReq createValidRequest() {
        PathToCdnFileReq request = new PathToCdnFileReq();
        request.setTenantId(12345L);
        request.setEmployeeId(67890L);
        request.setBusiness("test-business");
        request.setBusinessUnit("test-unit");
        request.setPath("N_");
        request.setName("test-image");
        request.setExtension("jpg");
        request.setHashCode("abc123");
        request.setTags(Arrays.asList("tag1", "tag-2", "tag_3"));
        request.setDescription("Test image description");
        return request;
    }
}
