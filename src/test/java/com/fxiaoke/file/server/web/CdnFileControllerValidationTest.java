package com.fxiaoke.file.server.web;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fxiaoke.file.server.domain.model.api.request.PathToCdnFileReq;
import com.fxiaoke.file.server.service.CdnFileService;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * CdnFileController 验证集成测试
 */
@WebMvcTest(CdnFileController.class)
@DisplayName("CdnFileController 验证集成测试")
class CdnFileControllerValidationTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private CdnFileService cdnFileService;

    @Test
    @DisplayName("有效请求应该通过验证并调用服务")
    void shouldPassValidationForValidRequest() throws Exception {
        // 准备有效请求
        PathToCdnFileReq validRequest = createValidRequest();
        
        // Mock 服务返回
        when(cdnFileService.pathToCdnFile(any(PathToCdnFileReq.class)))
            .thenReturn("https://cdn.example.com/path/to/file.jpg");

        // 执行请求
        mockMvc.perform(post("/FilesCdn/pathToCdnFile")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(validRequest)))
                .andExpect(status().isOk());

        // 验证服务被调用
        verify(cdnFileService).pathToCdnFile(any(PathToCdnFileReq.class));
    }

    @Test
    @DisplayName("无效的employeeId应该返回400错误")
    void shouldReturn400ForInvalidEmployeeId() throws Exception {
        PathToCdnFileReq invalidRequest = createValidRequest();
        invalidRequest.setEmployeeId(-999L); // 无效的employeeId

        mockMvc.perform(post("/FilesCdn/pathToCdnFile")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(invalidRequest)))
                .andExpect(status().isBadRequest());

        // 验证服务未被调用
        verify(cdnFileService, never()).pathToCdnFile(any(PathToCdnFileReq.class));
    }

    @Test
    @DisplayName("无效的路径类型应该返回400错误")
    void shouldReturn400ForInvalidPathType() throws Exception {
        PathToCdnFileReq invalidRequest = createValidRequest();
        invalidRequest.setPath("A_"); // 不支持的路径类型

        mockMvc.perform(post("/FilesCdn/pathToCdnFile")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(invalidRequest)))
                .andExpect(status().isBadRequest());

        // 验证服务未被调用
        verify(cdnFileService, never()).pathToCdnFile(any(PathToCdnFileReq.class));
    }

    @Test
    @DisplayName("不支持的文件扩展名应该返回400错误")
    void shouldReturn400ForUnsupportedExtension() throws Exception {
        PathToCdnFileReq invalidRequest = createValidRequest();
        invalidRequest.setExtension("pdf"); // 不支持的扩展名

        mockMvc.perform(post("/FilesCdn/pathToCdnFile")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(invalidRequest)))
                .andExpect(status().isBadRequest());

        // 验证服务未被调用
        verify(cdnFileService, never()).pathToCdnFile(any(PathToCdnFileReq.class));
    }

    @Test
    @DisplayName("无效的标签格式应该返回400错误")
    void shouldReturn400ForInvalidTags() throws Exception {
        PathToCdnFileReq invalidRequest = createValidRequest();
        invalidRequest.setTags(Arrays.asList("Invalid-Tag")); // 包含大写字母

        mockMvc.perform(post("/FilesCdn/pathToCdnFile")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(invalidRequest)))
                .andExpect(status().isBadRequest());

        // 验证服务未被调用
        verify(cdnFileService, never()).pathToCdnFile(any(PathToCdnFileReq.class));
    }

    @Test
    @DisplayName("系统调用请求应该通过验证")
    void shouldPassValidationForSystemCall() throws Exception {
        PathToCdnFileReq systemRequest = createValidRequest();
        systemRequest.setEmployeeId(-10000L); // 系统调用

        // Mock 服务返回
        when(cdnFileService.pathToCdnFile(any(PathToCdnFileReq.class)))
            .thenReturn("https://cdn.example.com/path/to/file.jpg");

        mockMvc.perform(post("/FilesCdn/pathToCdnFile")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(systemRequest)))
                .andExpect(status().isOk());

        // 验证服务被调用
        verify(cdnFileService).pathToCdnFile(any(PathToCdnFileReq.class));
    }

    @Test
    @DisplayName("缺少必填字段应该返回400错误")
    void shouldReturn400ForMissingRequiredFields() throws Exception {
        PathToCdnFileReq invalidRequest = new PathToCdnFileReq();
        // 不设置任何字段，所有必填字段都为空

        mockMvc.perform(post("/FilesCdn/pathToCdnFile")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(invalidRequest)))
                .andExpect(status().isBadRequest());

        // 验证服务未被调用
        verify(cdnFileService, never()).pathToCdnFile(any(PathToCdnFileReq.class));
    }

    /**
     * 创建一个有效的请求对象
     */
    private PathToCdnFileReq createValidRequest() {
        PathToCdnFileReq request = new PathToCdnFileReq();
        request.setTenantId(12345L);
        request.setEmployeeId(67890L);
        request.setBusiness("test-business");
        request.setBusinessUnit("test-unit");
        request.setPath("N_");
        request.setName("test-image");
        request.setExtension("jpg");
        request.setHashCode("abc123");
        request.setTags(Arrays.asList("tag1", "tag-2", "tag_3"));
        request.setDescription("Test image description");
        return request;
    }
}
